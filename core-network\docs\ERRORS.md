> Task :features:thinkingbox:compileDebugKotlin
> Task :features:thinkingbox:compileDebugKotlin FAILED
e: file:///D:/GymBro/GymBro/features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/adapter/ThinkingBoxStreamAdapter.kt:497:36 Unresolved reference 'thinkingBoxAdapter'.
e: file:///D:/GymBro/GymBro/features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/display/ThinkingBoxDisplayImpl.kt:53:58 Argument type mismatch: actual type is 'java.lang.Exception', but 'kotlin.String' was expected.
e: file:///D:/GymBro/GymBro/features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/display/ThinkingBoxDisplayImpl.kt:53:61 Null cannot be a value of a non-null type 'kotlin.Throwable'.
e: file:///D:/GymBro/GymBro/features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/display/ThinkingBoxDisplayImpl.kt:112:70 Argument type mismatch: actual type is 'java.lang.Exception', but 'kotlin.String' was expected.
e: file:///D:/GymBro/GymBro/features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/display/ThinkingBoxDisplayImpl.kt:112:73 Null cannot be a value of a non-null type 'kotlin.Throwable'.
e: file:///D:/GymBro/GymBro/features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/display/ThinkingBoxDisplayImpl.kt:146:33 No value passed for parameter 'sessionId'.
e: file:///D:/GymBro/GymBro/features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/display/ThinkingBoxDisplayImpl.kt:162:66 Argument type mismatch: actual type is 'java.lang.Exception', but 'kotlin.String' was expected.
e: file:///D:/GymBro/GymBro/features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/display/ThinkingBoxDisplayImpl.kt:162:69 Null cannot be a value of a non-null type 'kotlin.Throwable'.
e: file:///D:/GymBro/GymBro/features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/display/ThinkingBoxDisplayImpl.kt:200:25 No value passed for parameter 'sessionId'.
e: file:///D:/GymBro/GymBro/features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/service/ThinkingBoxLauncherImpl.kt:85:25 No value passed for parameter 'sessionId'.
e: file:///D:/GymBro/GymBro/features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/service/ThinkingBoxLauncherImpl.kt:100:66 Argument type mismatch: actual type is 'kotlin.Throwable', but 'kotlin.String' was expected.
e: file:///D:/GymBro/GymBro/features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/service/ThinkingBoxLauncherImpl.kt:100:73 Null cannot be a value of a non-null type 'kotlin.Throwable'.

