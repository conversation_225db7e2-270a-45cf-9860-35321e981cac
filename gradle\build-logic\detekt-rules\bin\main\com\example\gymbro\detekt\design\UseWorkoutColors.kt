package com.example.gymbro.detekt.design

import io.gitlab.arturbosch.detekt.api.*
import org.jetbrains.kotlin.psi.*

/**
 * 检查颜色获取方式
 * 
 * 规则：在健身相关的 UI 组件中，应该使用 MaterialTheme.workoutColors 
 * 而不是直接使用 MaterialTheme.colorScheme
 */
class UseWorkoutColors(config: Config = Config.empty) : Rule(config) {
    
    override val issue = Issue(
        javaClass.simpleName,
        Severity.CodeSmell,
        "应该使用 MaterialTheme.workoutColors 获取健身主题颜色。",
        Debt.FIVE_MINS
    )
    
    override fun visitDotQualifiedExpression(expression: KtDotQualifiedExpression) {
        super.visitDotQualifiedExpression(expression)
        
        val text = expression.text
        
        // 检查是否使用了 MaterialTheme.colorScheme 而不是 workoutColors
        if (text.contains("MaterialTheme.colorScheme") && isInWorkoutContext(expression)) {
            // 检查是否在健身相关的文件中
            val fileName = expression.containingKtFile.name
            if (isWorkoutRelatedFile(fileName)) {
                report(
                    CodeSmell(
                        issue,
                        Entity.from(expression),
                        "在健身相关组件中应该使用 MaterialTheme.workoutColors 而不是 MaterialTheme.colorScheme。"
                    )
                )
            }
        }
    }
    
    private fun isInWorkoutContext(expression: KtDotQualifiedExpression): Boolean {
        // 检查是否在 Composable 函数中
        var parent = expression.parent
        while (parent != null) {
            if (parent is KtNamedFunction) {
                val function = parent as KtNamedFunction
                // 检查是否有 @Composable 注解
                if (function.annotationEntries.any { 
                    it.shortName?.asString() == "Composable" 
                }) {
                    return true
                }
            }
            parent = parent.parent
        }
        return false
    }
    
    private fun isWorkoutRelatedFile(fileName: String): Boolean {
        val workoutKeywords = setOf(
            "workout", "exercise", "training", "session", "plan", 
            "template", "gym", "fitness", "coach"
        )
        
        val lowerFileName = fileName.lowercase()
        return workoutKeywords.any { keyword -> 
            lowerFileName.contains(keyword) 
        }
    }
}