package com.example.gymbro.buildlogic

import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.*

/**
 * GymBro项目代码质量统一管理插件
 *
 * 整合所有代码质量检查工具：
 * - Detekt: 静态代码分析
 * - Ktlint: 代码格式检查
 * - JaCoCo: 代码覆盖率统计
 * - GymBro约束验证：函数<80行、文件<500行、timber log限制
 * - 提供统一的质量检查任务
 * - 零警告标准配置
 */
class QualityConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            // 应用所有质量检查插件
            with(pluginManager) {
                apply("gymbro.detekt")
                apply("gymbro.ktlint")
                apply("gymbro.jacoco")
            }

            // 创建统一的质量检查任务
            tasks.register("qualityCheck") {
                description = "运行所有代码质量检查（GymBro约束 + Detekt + Ktlint + JaCoCo）"
                group = "verification"

                // 按照CICD文档建议的顺序：gymbroValidation -> ktlintCheck -> detekt -> assembleDebug
                dependsOn(
                    "gymbroValidation",     // 首先验证GymBro项目约束
                    "ktlintCheck",          // 再执行格式检查
                    "detektAll",            // 然后执行静态分析
                    "jacocoTestReport"      // 最后执行覆盖率统计
                )

                // 确保任务执行顺序
                tasks.findByName("ktlintCheck")?.mustRunAfter("gymbroValidation")
                tasks.findByName("detektAll")?.mustRunAfter("ktlintCheck")
                tasks.findByName("jacocoTestReport")?.mustRunAfter("detektAll")

                doLast {
                    val buildDir = project.layout.buildDirectory.get().asFile
                    println("=== ✅ GymBro 代码质量检查完成 ===")
                    println("✅ GymBro 项目约束: 通过")
                    println("✅ Ktlint 格式检查: 通过")
                    println("✅ Detekt 静态分析: 通过")
                    println("✅ JaCoCo 覆盖率统计: 完成")
                    println("📊 查看报告:")
                    println("   - Ktlint: ${buildDir}/reports/ktlint/")
                    println("   - Detekt: ${buildDir}/reports/detekt/")
                    println("   - JaCoCo: ${buildDir}/reports/jacoco/")
                    println("===============================")
                }
            }

            // 创建质量检查验证任务（包含覆盖率验证）
            tasks.register("qualityVerify") {
                description = "运行代码质量检查并验证覆盖率阈值"
                group = "verification"

                dependsOn(
                    "qualityCheck",
                    "jacocoCoverageVerification"
                )

                doLast {
                    println("=== GymBro 代码质量验证完成 ===")
                    println("✅ 所有质量检查通过")
                    println("✅ 覆盖率阈值验证通过")
                    println("🎉 代码质量达到生产标准")
                    println("===============================")
                }
            }

            // 创建代码格式化任务
            tasks.register("formatCode") {
                description = "格式化所有代码（Ktlint + Detekt）"
                group = "formatting"

                dependsOn(
                    "ktlintFormat"
                )

                // 可选：包含Detekt格式化（谨慎使用）
                // dependsOn("detektFormat")

                doLast {
                    println("=== GymBro 代码格式化完成 ===")
                    println("✅ Ktlint 格式化: 完成")
                    println("💡 提示: 请检查格式化后的代码变更")
                    println("=============================")
                }
            }

            // 创建快速质量检查任务（跳过测试）
            tasks.register("qualityCheckFast") {
                description = "快速代码质量检查（GymBro约束 + Detekt + Ktlint，跳过测试和覆盖率）"
                group = "verification"

                dependsOn(
                    "gymbroValidation",
                    "detektAll",
                    "ktlintCheck"
                )

                // 确保执行顺序
                tasks.findByName("ktlintCheck")?.mustRunAfter("gymbroValidation")
                tasks.findByName("detektAll")?.mustRunAfter("ktlintCheck")

                doLast {
                    println("=== GymBro 快速质量检查完成 ===")
                    println("✅ GymBro 项目约束: 通过")
                    println("✅ Detekt 静态分析: 通过")
                    println("✅ Ktlint 格式检查: 通过")
                    println("⚠️  跳过了测试覆盖率检查")
                    println("===============================")
                }
            }

            // 创建CI专用质量检查任务（符合文档流程）
            tasks.register("ciCheck") {
                description = "CI环境完整检查（gymbroValidation -> ktlint -> detekt -> danger -> build）"
                group = "verification"

                dependsOn(
                    "gymbroValidation",
                    "ktlintCheck",
                    "detektAll"
                    // danger-kotlin 由CI workflow单独执行
                )

                // 确保执行顺序
                tasks.findByName("ktlintCheck")?.mustRunAfter("gymbroValidation")
                tasks.findByName("detektAll")?.mustRunAfter("ktlintCheck")

                doLast {
                    println("=== 🚀 GymBro CI 检查完成 ===")
                    println("✅ GymBro 项目约束: 通过")
                    println("✅ 符合CICD文档标准流程")
                    println("✅ 代码质量达到生产标准")
                    println("==============================")
                }
            }

            // 创建质量报告汇总任务
            tasks.register("qualityReport") {
                description = "生成代码质量报告汇总"
                group = "reporting"

                dependsOn("qualityCheck")

                doLast {
                    val buildDir = layout.buildDirectory.get().asFile
                    val reportsDir = buildDir.resolve("reports")

                    println("=== GymBro 代码质量报告汇总 ===")
                    println("📊 报告位置:")

                    // 检查并列出所有报告
                    listOf(
                        "detekt/detekt.html" to "Detekt 静态分析",
                        "ktlint/ktlintMainSourceSetCheck.html" to "Ktlint 格式检查",
                        "jacoco/test/html/index.html" to "JaCoCo 覆盖率"
                    ).forEach { (path, name) ->
                        val reportFile = reportsDir.resolve(path)
                        if (reportFile.exists()) {
                            println("   ✅ $name: file://${reportFile.absolutePath}")
                        } else {
                            println("   ❌ $name: 报告未生成")
                        }
                    }

                    println("===============================")
                }
            }

            // 在Android项目中的特殊配置
            afterEvaluate {
                val isAndroidModule = project.plugins.hasPlugin("com.android.application") ||
                        project.plugins.hasPlugin("com.android.library")

                if (isAndroidModule) {
                    // Android项目特定的质量检查配置
                    tasks.named("qualityCheck") {
                        // 可以添加Android特定的检查
                        // 例如：dependsOn("lintDebug")
                    }
                }
            }
        }
    }
}
