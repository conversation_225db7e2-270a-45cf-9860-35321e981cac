package com.example.gymbro.detekt.mvi

import io.gitlab.arturbosch.detekt.api.*
import org.jetbrains.kotlin.psi.*

/**
 * 检查 State 类是否使用了 @Immutable 注解
 * 
 * 规则：所有名称包含 "State" 的数据类都应该使用 @Immutable 注解
 * 这确保了 Compose 的重组优化和 MVI 架构的一致性
 */
class ImmutableStateClass(config: Config = Config.empty) : Rule(config) {
    
    override val issue = Issue(
        javaClass.simpleName,
        Severity.CodeSmell,
        "State 类必须使用 @Immutable 注解以确保 Compose 性能优化。",
        Debt.FIVE_MINS
    )
    
    override fun visitClass(klass: KtClass) {
        super.visitClass(klass)
        
        val className = klass.name ?: return
        
        // 检查是否是 State 类
        if (isStateClass(klass, className)) {
            // 检查是否有 @Immutable 注解
            if (!hasImmutableAnnotation(klass)) {
                report(
                    CodeSmell(
                        issue,
                        Entity.from(klass),
                        "State 类 '$className' 缺少 @Immutable 注解。" +
                        "所有 State 类都应该使用 @Immutable 注解以确保 Compose 重组性能。"
                    )
                )
            }
            
            // 额外检查：确保是数据类
            if (!klass.isData()) {
                report(
                    CodeSmell(
                        issue,
                        Entity.from(klass),
                        "State 类 '$className' 应该是数据类（data class）。"
                    )
                )
            }
        }
    }
    
    private fun isStateClass(klass: KtClass, className: String): Boolean {
        // 类名包含 State
        if (className.contains("State")) return true
        
        // 实现了 UiState 接口
        val superTypes = klass.superTypeListEntries
        return superTypes.any { entry ->
            entry.text.contains("UiState") || entry.text.contains("State")
        }
    }
    
    private fun hasImmutableAnnotation(klass: KtClass): Boolean {
        return klass.annotationEntries.any { annotation ->
            val shortName = annotation.shortName?.asString()
            shortName == "Immutable" || shortName == "androidx.compose.runtime.Immutable"
        }
    }
}