16:30:34.308 TB-COACH                 I  🔍 AwaitingFirstToken状态: 实际AI消息ID=f98fd322-b2c0-4739-bdad-72453b5d6fb7
16:30:34.314 TB-VM                    D  🚀 [ViewModel初始化] ThinkingBoxViewModel启动
16:30:34.355 TB-COACH                 I  🚀 ThinkingBox激活: messageId=f98fd322-b2c0-4739-bdad-72453b5d6fb7, state=AwaitingFirstToken
16:30:34.356 TB-Thinkin...thCallback  I  TB-API: 🔗 设置ThinkingBox完成回调: sessionId=e890f39e-add4-439d-b3b9-e164a348a5f4, messageId=f98fd322-b2c0-4739-bdad-72453b5d6fb7
16:30:34.356 TB-Thinkin...thCallback  D  TB-API: 🚀 [初始化] messageId=f98fd322-b2c0-4739-bdad-72453b5d6fb7, sessionId=e890f39e-add4-439d-b3b9-e164a348a5f4
16:30:34.356 TB-REDUCER               D  🔄 [Intent处理] Initialize
16:30:34.357 TB-INIT                  I  🚀 [初始化] ThinkingBox: messageId=f98fd322-b2c0-4739-bdad-72453b5d6fb7
16:30:34.358 TB-EFFECT                D  ⚡ [Effect处理] StartTokenStreamListening
16:30:34.358 TB-STREAM                I  🎯 [Token流启动] messageId=f98fd322-b2c0-4739-bdad-72453b5d6fb7
16:30:34.359 TB-STREAM                I  ✅ [委托处理] 通过StreamAdapter处理: messageId=f98fd322-b2c0-4739-bdad-72453b5d6fb7
16:30:34.359 TB-STREAM                I  ✅ [等待事件] ViewModel准备接收ThinkingEvent: messageId=f98fd322-b2c0-4739-bdad-72453b5d6fb7
16:30:34.359 TB-Thinkin...oxInternal  D  TB-VM:  🚀 初始化ThinkingBox: f98fd322-b2c0-4739-bdad-72453b5d6fb7, hasTokenFlow=false
16:30:34.359 TB-Thinkin...elProvider  D  TB-Provider:TB-Provider: 🔗 [ThinkingBoxViewModelProvider] 注册ViewModel: messageId=f98fd322-b2c0-4739-bdad-72453b5d6fb7
16:30:34.359 TB-REDUCER               D  🔄 [Intent处理] Initialize
16:30:34.360 TB-INIT                  I  🚀 [初始化] ThinkingBox: messageId=f98fd322-b2c0-4739-bdad-72453b5d6fb7
16:30:34.360 TB-INIT                  D  ⏭️ [跳过重复] 初始化: f98fd322-b2c0-4739-bdad-72453b5d6fb7
16:30:34.360 TB-Thinkin...oxInternal  D  TB-VM:  🔗 连接HistoryActor到Effect流: f98fd322-b2c0-4739-bdad-72453b5d6fb7
16:30:34.360 TB-HistoryActor          I  TB-History: 🚀 [初始化] 开始监听ThinkingBox Effect流
16:30:34.377 Coach-ThinkingBox        D  🔍 [显示决策] isStreaming=true
16:30:34.380 TB-COACH                 I  🔍 AwaitingFirstToken状态: 实际AI消息ID=f98fd322-b2c0-4739-bdad-72453b5d6fb7
16:30:34.398 Coach-ThinkingBox        D  🔍 [显示决策] isStreaming=true
16:30:34.511 .example.gymbro          I  Compiler allocated 6181KB to compile void androidx.compose.material3.TextFieldKt.TextFieldLayout(androidx.compose.ui.Modifier, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function3, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, boolean, float, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, androidx.compose.foundation.layout.PaddingValues, androidx.compose.runtime.Composer, int, int)
16:30:45.405 .example.gymbro          I  Background concurrent mark compact GC freed 25MB AllocSpace bytes, 1(884KB) LOS objects, 20% free, 95MB/119MB, paused 1.310ms,3.860ms total 288.842ms
16:30:50.260 CNET-REST-Client         I  ✅ [请求成功] POST 响应=848130字符, 耗时=15981ms
16:30:50.274 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='...', 长度变化=345->0
16:30:50.274 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='...', 长度变化=344->0
16:30:50.274 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='...', 长度变化=345->0
16:30:50.275 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='...', 长度变化=344->0
16:30:50.275 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='...', 长度变化=345->0
16:30:50.276 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='...', 长度变化=344->0
16:30:50.276 CNET-JSON-TRACE          D  ⏰ 超时触发: 20个token, 超时20516ms
16:30:50.277 CNET-JSON-TRACE          E  📦 [批量输出 20 tokens]
                                         📊 批量摘要: 20个token条目 (总计4340个)
16:30:50.277 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='...', 长度变化=345->0
16:30:50.278 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='...', 长度变化=345->0
16:30:50.278 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='...', 长度变化=345->0
16:30:50.279 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='...', 长度变化=345->0
16:30:50.279 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='...', 长度变化=344->0
16:30:50.280 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='...', 长度变化=344->0
16:30:50.280 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='...', 长度变化=345->0
16:30:50.281 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='...', 长度变化=344->0
16:30:50.301 CNET-JSON-TRACE          D  🎯 批量触发: 达到100个token
16:30:50.301 CNET-JSON-TRACE          E  📦 [批量输出 100 tokens]
                                         📊 批量摘要: 100个token条目 (总计4440个)
                                         ⏱️ 时间跨度: 25ms
                                         📈 统计: 输入0个, 跳过0个, 处理0个, 错误0个
                                         📝 前10条内容:
                                           🔍 [统一SSE解析] token='data: {"id":"gen-1754209835-ncTF91sX1YsWcJdoUGWI",...'
                                           ⚠️ [JSON解析] 未找到有效内容
                                           🔍 [统一SSE解析] token='data: {"id":"gen-1754209835-ncTF91sX1YsWcJdoUGWI",...'
                                           ⚠️ [JSON解析] 未找到有效内容
                                           🔍 [统一SSE解析] token='data: {"id":"gen-1754209835-ncTF91sX1YsWcJdoUGWI",...'
                                           ⚠️ [JSON解析] 未找到有效内容
                                           🔍 [统一SSE解析] token='data: {"id":"gen-1754209835-ncTF91sX1YsWcJdoUGWI",...'
                                           ⚠️ [JSON解析] 未找到有效内容
                                           🔍 [统一SSE解析] token='data: {"id":"gen-1754209835-ncTF91sX1YsWcJdoUGWI",...'
                                           ⚠️ [JSON解析] 未找到有效内容
                                           ... 还有90条 ...
16:30:50.509 TB-E2E-TRACE             E  🔍 [输出通道接收] conversationId=f98fd322-b2c0-4739-bdad-72453b5d6fb7, contentType=JSON_SSE, token='恭喜...'
16:30:50.509 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='你...', 长度变化=346->1
16:30:50.510 COACH-NEW                D  📥 AI供应商响应token: 恭喜...
16:30:50.510 TB-E2E-TRACE             E  🔍 [输出通道接收] conversationId=f98fd322-b2c0-4739-bdad-72453b5d6fb7, contentType=JSON_SSE, token='你...'
16:30:50.511 COACH-NEW                D  📥 AI供应商响应token: 你...
16:30:50.511 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='决定...', 长度变化=347->2
16:30:50.511 TB-E2E-TRACE             E  🔍 [输出通道接收] conversationId=f98fd322-b2c0-4739-bdad-72453b5d6fb7, contentType=JSON_SSE, token='决定...'
16:30:50.512 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='开始...', 长度变化=347->2
16:30:50.512 COACH-NEW                D  📥 AI供应商响应token: 决定...
16:30:50.513 TB-E2E-TRACE             E  🔍 [输出通道接收] conversationId=f98fd322-b2c0-4739-bdad-72453b5d6fb7, contentType=JSON_SSE, token='开始...'
16:30:50.515 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='力量...', 长度变化=347->2
16:30:50.515 COACH-NEW                D  📥 AI供应商响应token: 开始...
16:30:50.515 TB-E2E-TRACE             E  🔍 [输出通道接收] conversationId=f98fd322-b2c0-4739-bdad-72453b5d6fb7, contentType=JSON_SSE, token='力量...'
16:30:50.516 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='训练...', 长度变化=347->2
16:30:50.516 TB-E2E-TRACE             E  🔍 [输出通道接收] conversationId=f98fd322-b2c0-4739-bdad-72453b5d6fb7, contentType=JSON_SSE, token='训练...'
16:30:50.516 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='！...', 长度变化=346->1
16:30:50.517 TB-E2E-TRACE             E  🔍 [输出通道接收] conversationId=f98fd322-b2c0-4739-bdad-72453b5d6fb7, contentType=JSON_SSE, token='！...'
16:30:50.517 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='这是一个...', 长度变化=349->4
16:30:50.517 TB-E2E-TRACE             E  🔍 [输出通道接收] conversationId=f98fd322-b2c0-4739-bdad-72453b5d6fb7, contentType=JSON_SSE, token='这是一个...'
16:30:50.518 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='非常...', 长度变化=347->2
16:30:50.518 TB-E2E-TRACE             E  🔍 [输出通道接收] conversationId=f98fd322-b2c0-4739-bdad-72453b5d6fb7, contentType=JSON_SSE, token='非常...'
16:30:50.519 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='棒...', 长度变化=346->1
16:30:50.520 TB-E2E-TRACE             E  🔍 [输出通道接收] conversationId=f98fd322-b2c0-4739-bdad-72453b5d6fb7, contentType=JSON_SSE, token='棒...'
16:30:50.520 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='的...', 长度变化=346->1
16:30:50.521 TB-E2E-TRACE             E  🔍 [输出通道接收] conversationId=f98fd322-b2c0-4739-bdad-72453b5d6fb7, contentType=JSON_SSE, token='的...'
16:30:50.522 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='决定...', 长度变化=347->2
16:30:50.522 TB-E2E-TRACE             E  🔍 [输出通道接收] conversationId=f98fd322-b2c0-4739-bdad-72453b5d6fb7, contentType=JSON_SSE, token='决定...'
16:30:50.523 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='，...', 长度变化=346->1
16:30:50.523 TB-E2E-TRACE             E  🔍 [输出通道接收] conversationId=f98fd322-b2c0-4739-bdad-72453b5d6fb7, contentType=JSON_SSE, token='，...'
16:30:50.524 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='不仅能...', 长度变化=348->3
16:30:50.524 TB-E2E-TRACE             E  🔍 [输出通道接收] conversationId=f98fd322-b2c0-4739-bdad-72453b5d6fb7, contentType=JSON_SSE, token='不仅能...'
16:30:50.525 CNET-PROCESSOR-Fix       E  ✅ [处理结果] 输入='data: {"id":"gen-175...', 输出='增强...', 长度变化=347->2
16:30:50.525 TB-E2E-TRACE             E  🔍 [输出通道接收] conversationId=f98fd322-b2c0-4739-bdad-72453b5d6fb7, contentType=JSON_SSE, token='增强...'
16:30:50.526 COACH-NEW                D  📥 AI供应商响应token: 力量...
16:30:50.526 COACH-NEW                D  📥 AI供应商响应token: 训练...
16:30:50.526 COACH-NEW                D  📥 AI供应商响应token: ！...
16:30:50.526 COACH-NEW                D  📥 AI供应商响应token: 这是一个...
16:30:50.526 COACH-NEW                D  📥 AI供应商响应token: 非常...
16:30:50.526 COACH-NEW                D  📥 AI供应商响应token: 棒...
