package com.example.gymbro.detekt.logging

import io.gitlab.arturbosch.detekt.api.*
import org.jetbrains.kotlin.psi.*

/**
 * 检查不同模块的日志使用规范
 * 
 * 规则：
 * - Domain 层应该使用 log.*（避免 Android 依赖）
 * - 其他层可以使用 Timber.*
 */
class LoggingModuleRestriction(config: Config = Config.empty) : Rule(config) {
    
    override val issue = Issue(
        javaClass.simpleName,
        Severity.CodeSmell,
        "日志使用不符合模块层次规范。Domain 层应使用 log，其他层使用 Timber。",
        Debt.FIVE_MINS
    )
    
    override fun visitDotQualifiedExpression(expression: KtDotQualifiedExpression) {
        super.visitDotQualifiedExpression(expression)
        
        val text = expression.text
        val filePath = expression.containingKtFile.virtualFilePath?.path ?: return
        
        // 检查是否在 domain 模块中
        if (isDomainModule(filePath)) {
            // Domain 层不应该使用 Timber
            if (text.startsWith("Timber.")) {
                report(
                    CodeSmell(
                        issue,
                        Entity.from(expression),
                        "Domain 层不应该使用 Timber（Android 依赖）。请使用 log.* 进行日志记录。"
                    )
                )
            }
        } else {
            // 非 Domain 层建议使用 Timber 而不是原始 log
            if (text.startsWith("log.") && !isAllowedLogUsage(text)) {
                report(
                    CodeSmell(
                        issue,
                        Entity.from(expression),
                        "建议在非 Domain 层使用 Timber 而不是原始 log，以获得更好的日志功能。"
                    )
                )
            }
        }
    }
    
    private fun isDomainModule(filePath: String): Boolean {
        return filePath.contains("/domain/") || 
               filePath.contains("\\domain\\")
    }
    
    private fun isAllowedLogUsage(text: String): Boolean {
        // 某些场景下允许使用 log，比如性能测量
        return text.contains("log.performance") ||
               text.contains("log.measure") ||
               text.contains("System.currentTimeMillis")
    }
}