package com.example.gymbro.detekt.design

import io.gitlab.arturbosch.detekt.api.*
import org.jetbrains.kotlin.psi.*
import org.jetbrains.kotlin.psi.psiUtil.getCallNameExpression

/**
 * 检查硬编码的设计值使用
 * 
 * 规则：禁止直接使用硬编码的颜色、尺寸、字体大小等设计值
 * 应该使用 GymBroTokens, GymBroColors, GymBroSpacing 等设计系统
 */
class NoHardcodedDesignValues(config: Config = Config.empty) : Rule(config) {
    
    override val issue = Issue(
        javaClass.simpleName,
        Severity.CodeSmell,
        "禁止硬编码设计值。应该使用 GymBroTokens 设计系统。",
        Debt.TEN_MINS
    )
    
    // 硬编码颜色模式（十六进制）
    private val hexColorRegex = Regex("""Color\(0x[0-9A-Fa-f]{8}\)""")
    
    // 硬编码尺寸模式
    private val hardcodedSizeRegex = Regex("""\d+\.dp""")
    
    // 硬编码字体大小模式
    private val hardcodedFontSizeRegex = Regex("""\d+\.sp""")
    
    override fun visitCallExpression(expression: KtCallExpression) {
        super.visitCallExpression(expression)
        
        val callText = expression.text
        
        // 检查硬编码颜色
        if (hexColorRegex.containsMatchIn(callText)) {
            report(
                CodeSmell(
                    issue,
                    Entity.from(expression),
                    "发现硬编码颜色值。应该使用 GymBroColors 或 MaterialTheme.colorScheme。"
                )
            )
        }
        
        // 检查硬编码尺寸
        if (hardcodedSizeRegex.containsMatchIn(callText) && !isAllowedHardcodedSize(callText)) {
            report(
                CodeSmell(
                    issue,
                    Entity.from(expression),
                    "发现硬编码尺寸值。应该使用 GymBroSpacing 或 GymBroTokens.sizing。"
                )
            )
        }
        
        // 检查硬编码字体大小
        if (hardcodedFontSizeRegex.containsMatchIn(callText)) {
            report(
                CodeSmell(
                    issue,
                    Entity.from(expression),
                    "发现硬编码字体大小。应该使用 GymBroTypography 或 MaterialTheme.typography。"
                )
            )
        }
    }
    
    override fun visitProperty(property: KtProperty) {
        super.visitProperty(property)
        
        val initializer = property.initializer?.text ?: return
        
        // 检查属性初始化中的硬编码值
        if (hexColorRegex.containsMatchIn(initializer)) {
            report(
                CodeSmell(
                    issue,
                    Entity.from(property),
                    "属性 '${property.name}' 使用了硬编码颜色值。应该使用 GymBroColors。"
                )
            )
        }
    }
    
    /**
     * 允许的硬编码尺寸场景
     * 例如：0.dp, 1.dp 这种基础值
     */
    private fun isAllowedHardcodedSize(text: String): Boolean {
        return text.contains("0.dp") || 
               text.contains("1.dp") ||
               text.contains("(-1).dp") // 通常用于占满父容器
    }
}