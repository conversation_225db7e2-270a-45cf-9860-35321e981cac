package com.example.gymbro.detekt.mvi

import io.gitlab.arturbosch.detekt.api.*
import org.jetbrains.kotlin.psi.*
import org.jetbrains.kotlin.psi.psiUtil.containingClass

/**
 * 检查 Intent 类的命名规范
 * 
 * 规则：
 * 1. Intent 类应该以动词开头（Load, Update, Create, Delete, Toggle 等）
 * 2. 结果 Intent 应该以 "Result" 结尾
 * 3. 内部 Intent 应该以 "Internal" 开头
 */
class MviIntentNaming(config: Config = Config.empty) : Rule(config) {
    
    override val issue = Issue(
        javaClass.simpleName,
        Severity.CodeSmell,
        "Intent 类命名不符合 MVI 规范。Intent 应该以动词开头，结果 Intent 以 Result 结尾。",
        Debt.FIVE_MINS
    )
    
    private val actionVerbs = setOf(
        "Load", "Update", "Create", "Delete", "Toggle", "Refresh", "Save", "Submit",
        "Cancel", "Reset", "Clear", "Add", "Remove", "Edit", "Navigate", "Show", "Hide"
    )
    
    override fun visitClass(klass: KtClass) {
        super.visitClass(klass)
        
        // 检查是否是 Intent 接口的实现或者类名包含 Intent
        if (isIntentClass(klass)) {
            val className = klass.name ?: return
            
            // 跳过基础 Intent 接口
            if (className == "Intent" || className == "AppIntent") return
            
            // 检查命名规范
            if (!isValidIntentName(className)) {
                report(
                    CodeSmell(
                        issue,
                        Entity.from(klass),
                        "Intent '$className' 命名不符合规范。" +
                        "Intent 应该以动词开头（${actionVerbs.joinToString(", ")}），" +
                        "结果 Intent 以 'Result' 结尾，内部 Intent 以 'Internal' 开头。"
                    )
                )
            }
        }
    }
    
    private fun isIntentClass(klass: KtClass): Boolean {
        // 检查是否实现了 Intent 接口
        val superTypes = klass.superTypeListEntries
        return superTypes.any { entry ->
            entry.text.contains("Intent") || entry.text.contains("AppIntent")
        } || 
        // 或者类名包含 Intent
        (klass.name?.contains("Intent") == true)
    }
    
    private fun isValidIntentName(className: String): Boolean {
        // 内部 Intent 规则
        if (className.startsWith("Internal")) {
            return true
        }
        
        // 结果 Intent 规则
        if (className.endsWith("Result")) {
            val baseName = className.removeSuffix("Result")
            return actionVerbs.any { verb -> baseName.startsWith(verb) }
        }
        
        // 普通 Intent 规则
        return actionVerbs.any { verb -> className.startsWith(verb) }
    }
}