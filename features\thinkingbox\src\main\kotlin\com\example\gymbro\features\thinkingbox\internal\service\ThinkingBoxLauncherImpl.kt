package com.example.gymbro.features.thinkingbox.internal.service

import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.features.thinkingbox.api.ThinkingBoxCompletionListener
import com.example.gymbro.features.thinkingbox.api.ThinkingBoxError
import com.example.gymbro.features.thinkingbox.api.ThinkingBoxLauncher
import com.example.gymbro.features.thinkingbox.api.ThinkingBoxStatus
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.adapter.ThinkingBoxStreamAdapter
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ThinkingBoxLauncher实现类
 *
 * 🎯 【最终正确架构】核心职责：
 * - 被动订阅DirectOutputChannel的数据流
 * - 专注于业务逻辑和状态管理
 * - 不发起AI请求，只处理接收到的OutputToken
 * - 可选择性地使用ThinkingBoxStreamAdapter进行高级处理
 *
 * 🔥 正确的数据流：
 * Coach → AiStreamRepository → Core-Network → DirectOutputChannel → ThinkingBoxLauncher
 * - ThinkingBox直接订阅DirectOutputChannel（主要路径）
 * - ThinkingBoxStreamAdapter用于需要复杂处理的场景（可选）
 * - 保持架构简洁，避免不必要的中间层
 */
@Singleton
class ThinkingBoxLauncherImpl @Inject constructor(
    private val thinkingBoxStreamAdapter: ThinkingBoxStreamAdapter,
    private val ioDispatcher: CoroutineDispatcher,
) : ThinkingBoxLauncher {

    private val scope = CoroutineScope(SupervisorJob() + ioDispatcher)
    private val activeJobs = ConcurrentHashMap<String, Job>()
    private val processingStatus = ConcurrentHashMap<String, ThinkingBoxStatus>()

    override suspend fun startThinking(
        messageId: String,
        userPrompt: String,
        completionListener: ThinkingBoxCompletionListener,
    ): ModernResult<Unit> {
        Timber.tag("TB-LAUNCHER-Service").d("🚀 开始订阅思考数据流: messageId=$messageId")

        return try {
            // 取消之前的订阅（如果存在）
            cancelThinking(messageId)

            // 设置初始状态
            processingStatus[messageId] = ThinkingBoxStatus.Processing()

            // 🎯 【架构解耦】使用ThinkingBoxStreamAdapter进行高级处理
            // 不再直接订阅DirectOutputChannel，而是使用专门的适配器
            // 注意：userPrompt参数在这里不使用，因为AI请求由Coach模块发起
            Timber.tag("TB-LAUNCHER-Service").d("🚀 启动ThinkingBoxStreamAdapter处理: messageId=$messageId")

            val streamingJob = thinkingBoxStreamAdapter.startDirectOutputProcessing(
                messageId = messageId,
                onTokenReceived = { thinkingEvent ->
                    Timber.tag(
                        "TB-LAUNCHER-Service",
                    ).d("📥 接收ThinkingEvent: messageId=$messageId, event=${thinkingEvent::class.simpleName}")
                    // 🎯 处理ThinkingEvent，转换为业务逻辑
                    processThinkingEvent(thinkingEvent, completionListener)
                },
                onStreamComplete = {
                    Timber.tag("TB-LAUNCHER-Service").d("✅ ThinkingBox流式处理完成: messageId=$messageId")
                    processingStatus[messageId] = ThinkingBoxStatus.Completed(
                        completedAt = System.currentTimeMillis(),
                        duration = 0,
                    )
                    completionListener.onDisplayComplete(
                        messageId = messageId,
                        sessionId = "",
                        thinkingProcess = "思考过程已完成",
                        finalContent = "内容处理完成",
                        metadata = mapOf(
                            "completedAt" to System.currentTimeMillis(),
                            "source" to "ThinkingBoxStreamAdapter",
                        ),
                    )
                },
                onError = { error ->
                    Timber.e(
                        error,
                        "TB-LAUNCHER-Service: ❌ ThinkingBoxStreamAdapter处理失败: messageId=$messageId",
                    )
                    processingStatus[messageId] = ThinkingBoxStatus.Failed(
                        error = ThinkingBoxError.AiRequestFailed(error.message ?: "流式处理失败"),
                        failedAt = System.currentTimeMillis(),
                    )
                    completionListener.onDisplayError(messageId, "", error, null)
                },
            )

            // 将StreamingJob转换为普通Job进行管理
            val job = streamingJob.job

            activeJobs[messageId] = job

            Timber.d("TB-LAUNCHER-Service: ✅ ThinkingBoxStreamAdapter处理已启动: messageId=$messageId")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "TB-LAUNCHER-Service: ❌ 启动失败: messageId=$messageId")
            processingStatus[messageId] = ThinkingBoxStatus.Failed(
                error = ThinkingBoxError.UnknownError(e.message ?: "Unknown error"),
                failedAt = System.currentTimeMillis(),
            )
            ModernResult.Error(
                error = ModernDataError(
                    operationName = "startThinking",
                    errorType = GlobalErrorType.System.General,
                    category = ErrorCategory.SYSTEM,
                    cause = e,
                ),
            )
        }
    }

    override suspend fun startAiProcessing(
        request: com.example.gymbro.features.thinkingbox.api.ThinkingBoxRequest,
        completionListener: ThinkingBoxCompletionListener,
    ): ModernResult<Unit> {
        Timber.tag("TB-LAUNCHER-Service").d("🚀 启动AI处理（完整请求）: messageId=${request.messageId}")

        return try {
            // 验证请求有效性
            if (!request.isValid()) {
                return ModernResult.Error(
                    error = ModernDataError(
                        operationName = "startAiProcessing",
                        errorType = GlobalErrorType.Validation.InvalidInput,
                        category = ErrorCategory.VALIDATION,
                        cause = IllegalArgumentException("ThinkingBox请求数据无效"),
                    ),
                )
            }

            // 委托给核心的startThinking方法
            startThinking(
                messageId = request.messageId,
                userPrompt = request.userMessage,
                completionListener = completionListener,
            )
        } catch (e: Exception) {
            Timber.e(e, "TB-LAUNCHER-Service: ❌ AI处理启动失败: messageId=${request.messageId}")
            ModernResult.Error(
                error = ModernDataError(
                    operationName = "startAiProcessing",
                    errorType = GlobalErrorType.System.General,
                    category = ErrorCategory.SYSTEM,
                    cause = e,
                ),
            )
        }
    }

    override suspend fun cancelThinking(messageId: String): ModernResult<Unit> {
        Timber.d("TB-LAUNCHER-Service: 🛑 取消AI内容生成: messageId=$messageId")

        return try {
            activeJobs[messageId]?.cancel()
            activeJobs.remove(messageId)
            processingStatus[messageId] = ThinkingBoxStatus.Idle

            Timber.d("TB-LAUNCHER-Service: ✅ AI内容生成已取消: messageId=$messageId")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "TB-LAUNCHER-Service: ❌ 取消操作失败: messageId=$messageId")
            ModernResult.Error(
                error = ModernDataError(
                    operationName = "cancelThinking",
                    errorType = GlobalErrorType.System.General,
                    category = ErrorCategory.SYSTEM,
                    cause = e,
                ),
            )
        }
    }

    override suspend fun getThinkingStatus(messageId: String): ModernResult<ThinkingBoxStatus> {
        Timber.d("TB-LAUNCHER-Service: 🔍 查询处理状态: messageId=$messageId")

        return try {
            val status = processingStatus[messageId] ?: ThinkingBoxStatus.Idle
            ModernResult.Success(status)
        } catch (e: Exception) {
            Timber.e(e, "TB-LAUNCHER-Service: ❌ 状态查询失败: messageId=$messageId")
            ModernResult.Error(
                error = ModernDataError(
                    operationName = "getThinkingStatus",
                    errorType = GlobalErrorType.System.General,
                    category = ErrorCategory.SYSTEM,
                    cause = e,
                ),
            )
        }
    }

    override suspend fun getActiveThinkingSessions(): ModernResult<List<String>> {
        Timber.d("TB-Launcher: 📋 获取活跃处理会话列表")

        return try {
            val activeSessions = activeJobs.keys.toList()
            Timber.d("TB-Launcher: ✅ 活跃处理会话数量: ${activeSessions.size}")
            ModernResult.Success(activeSessions)
        } catch (e: Exception) {
            Timber.e(e, "TB-Launcher: ❌ 获取活跃会话失败")
            ModernResult.Error(
                error = ModernDataError(
                    operationName = "getActiveThinkingSessions",
                    errorType = GlobalErrorType.System.General,
                    category = ErrorCategory.SYSTEM,
                    cause = e,
                ),
            )
        }
    }

    override suspend fun clearAllThinkingSessions(): ModernResult<Unit> {
        Timber.d("TB-Launcher: 🧹 清理所有活跃会话")

        return try {
            activeJobs.values.forEach { job ->
                job.cancel()
            }
            activeJobs.clear()
            processingStatus.clear()

            Timber.d("TB-Launcher: ✅ 所有活跃会话已清理")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "TB-Launcher: ❌ 清理会话失败")
            ModernResult.Error(
                error = ModernDataError(
                    operationName = "clearAllThinkingSessions",
                    errorType = GlobalErrorType.System.General,
                    category = ErrorCategory.SYSTEM,
                    cause = e,
                ),
            )
        }
    }

    /**
     * 🎯 处理从ThinkingBoxStreamAdapter接收到的ThinkingEvent
     *
     * ThinkingBox专注于高级事件处理和业务逻辑
     * 基于Segment队列架构事件系统
     */
    private fun processThinkingEvent(
        thinkingEvent: ThinkingEvent,
        completionListener: ThinkingBoxCompletionListener,
    ) {
        try {
            // 🎯 根据实际的ThinkingEvent类型进行处理
            when (thinkingEvent) {
                is ThinkingEvent.SegmentStarted -> {
                    Timber.tag(
                        "TB-LAUNCHER-Event",
                    ).d(
                        "🚀 段开始: id=${thinkingEvent.id}, kind=${thinkingEvent.kind}, title=${thinkingEvent.title}",
                    )
                    // 处理新段开始事件
                }
                is ThinkingEvent.SegmentText -> {
                    Timber.tag("TB-LAUNCHER-Event").v("📝 段文本: ${thinkingEvent.text.take(100)}...")
                    // 处理段文本内容
                }
                is ThinkingEvent.SegmentClosed -> {
                    Timber.tag("TB-LAUNCHER-Event").d("✅ 段闭合: id=${thinkingEvent.id}")
                    // 处理段闭合事件
                }
                is ThinkingEvent.UiSegmentRendered -> {
                    Timber.tag("TB-LAUNCHER-Event").d("🎨 UI段渲染完成: id=${thinkingEvent.id}")
                    // 处理UI段渲染完成事件
                }
                is ThinkingEvent.ThinkingClosed -> {
                    Timber.tag("TB-LAUNCHER-Event").d("🔚 思考阶段结束")
                    // 处理思考阶段结束事件
                }
                is ThinkingEvent.FinalStart -> {
                    Timber.tag("TB-LAUNCHER-Event").d("📋 最终内容开始")
                    // 处理最终内容开始事件
                }
                is ThinkingEvent.FinalContent -> {
                    Timber.tag("TB-LAUNCHER-Event").v("📄 最终内容: ${thinkingEvent.text.take(100)}...")
                    // 处理最终内容文本
                }
                is ThinkingEvent.FinalComplete -> {
                    Timber.tag("TB-LAUNCHER-Event").d("✅ 最终内容完成")
                    // 处理最终内容完成事件
                }
                is ThinkingEvent.ParseError -> {
                    Timber.tag("TB-LAUNCHER-Event").w("⚠️ 解析错误: ${thinkingEvent.message}")
                    // 处理解析错误
                }
            }

            // 这里可以添加更多ThinkingBox特有的业务逻辑
            // 例如：状态更新、UI通知、数据持久化等
        } catch (e: Exception) {
            Timber.e(e, "TB-LAUNCHER-Event: ❌ ThinkingEvent处理失败: event=${thinkingEvent::class.simpleName}")
        }
    }
}
