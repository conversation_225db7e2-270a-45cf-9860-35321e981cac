package com.example.gymbro.buildlogic

import com.example.gymbro.buildlogic.utils.libs
import io.gitlab.arturbosch.detekt.Detekt
import io.gitlab.arturbosch.detekt.DetektCreateBaselineTask
import io.gitlab.arturbosch.detekt.extensions.DetektExtension
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.*

/**
 * GymBro项目Detekt静态代码分析统一管理插件
 *
 * 配置统一的Kotlin静态代码分析规则：
 * - 基于项目自定义detekt.yml配置
 * - 支持baseline文件管理
 * - 生成多种格式报告
 * - 零警告标准配置
 * - 强制执行：函数<80行、文件<500行、timber log限制
 */
class DetektConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            // 应用Detekt插件
            pluginManager.apply("io.gitlab.arturbosch.detekt")
            
            // TODO: 重新启用自定义规则依赖
            // dependencies {
            //     add("detektPlugins", project(":build-logic:detekt-rules"))
            // }

            // 配置Detekt扩展
            extensions.configure<DetektExtension> {
                // 使用项目根目录的配置文件
                config.setFrom(rootProject.files("config/detekt/detekt.yml"))

                // 使用baseline文件（如果存在）
                baseline = rootProject.file("config/detekt/baseline.xml")

                // 启用并行执行
                parallel = true

                // 构建失败时的行为
                buildUponDefaultConfig = true
                allRules = false

                // 构建失败时的行为 - CI环境中严格检查，本地环境允许失败
                ignoreFailures = !project.hasProperty("ci")

                // 自动修正（默认关闭，仅在 detektFormat 任务中启用）
                autoCorrect = false

                // 配置源文件
                source.setFrom(
                    files(
                        "src/main/kotlin",
                        "src/main/java",
                        "src/test/kotlin",
                        "src/test/java"
                    )
                )
            }

            // 配置Detekt任务
            tasks.withType<Detekt>().configureEach {
                description = "运行Detekt静态代码分析"
                group = "verification"

                // 设置JVM目标版本（直接赋值）
                jvmTarget = "17"

                // 配置报告
                reports {
                    html.required.set(true)
                    xml.required.set(true)
                    txt.required.set(false)
                    sarif.required.set(false)
                    md.required.set(false)

                    // 自定义报告位置
                    html.outputLocation.set(layout.buildDirectory.file("reports/detekt/detekt.html"))
                    xml.outputLocation.set(layout.buildDirectory.file("reports/detekt/detekt.xml"))
                }

                // 排除生成的代码
                exclude("**/generated/**", "**/build/**", "**/resources/**")

                // 包含源文件
                include("**/*.kt", "**/*.kts")

                // 安全设置类路径 - 使用正确的可解析配置
                try {
                    // 使用 runtimeClasspath 替代 compileClasspath
                    val runtimeClasspath = configurations.findByName("runtimeClasspath")
                    if (runtimeClasspath?.isCanBeResolved == true) {
                        classpath.setFrom(runtimeClasspath)
                    }
                } catch (e: Exception) {
                    // 如果没有可用的类路径配置，保持为空
                    logger.warn("无法设置 Detekt 类路径: ${e.message}")
                }

                // CI环境中的检查逻辑已在DetektExtension中统一处理
            }

            // 配置baseline创建任务
            tasks.withType<DetektCreateBaselineTask>().configureEach {
                description = "创建或更新Detekt baseline文件"
                group = "verification"

                // 设置JVM目标版本（直接赋值）
                jvmTarget = "17"

                // 设置baseline文件位置
                baseline.set(rootProject.file("config/detekt/baseline.xml"))
            }

            // 创建便利任务
            tasks.register("detektAll") {
                description = "运行所有源集的Detekt检查"
                group = "verification"
                dependsOn(tasks.withType<Detekt>())

                doLast {
                    println("=== GymBro Detekt 静态分析完成 ===")
                    println("HTML报告位置：${layout.buildDirectory.get().asFile}/reports/detekt/detekt.html")
                    println("XML报告位置：${layout.buildDirectory.get().asFile}/reports/detekt/detekt.xml")
                    println("================================")
                }
            }

            // 当从命令行调用 detektFormat 时，动态配置 Detekt 任务以进行格式化
            gradle.taskGraph.whenReady {
                val isFormatting = gradle.startParameter.taskNames.any { name ->
                    // 支持 "detektFormat", ":app:detektFormat" 等形式
                    name.endsWith("detektFormat")
                }

                if (isFormatting) {
                    tasks.withType<Detekt>().configureEach {
                        logger.info("为任务 ${this.path} 启用 detekt 自动格式化")
                        autoCorrect = true
                        ignoreFailures = true // 格式化不应使构建失败
                    }
                }
            }

            // 创建自定义验证任务
            tasks.register("gymbroValidation") {
                description = "验证GymBro项目约束：函数<80行、文件<500行、timber log限制"
                group = "verification"

                doLast {
                    println("=== GymBro 项目约束验证 ===")
                    
                    var hasViolations = false
                    val sourceFiles = fileTree("src") {
                        include("**/*.kt")
                        exclude("**/test/**", "**/androidTest/**", "**/build/**")
                    }
                    
                    sourceFiles.forEach { file ->
                        val lines = file.readLines()
                        val lineCount = lines.size
                        
                        // 检查文件行数约束
                        if (lineCount > 500) {
                            logger.error("❌ 文件行数超限: ${file.relativeTo(projectDir)} ($lineCount 行 > 500 行)")
                            hasViolations = true
                        }
                        
                        // 检查函数行数和timber log约束
                        var currentFunctionLines = 0
                        var currentFunctionTimberCount = 0
                        var inFunction = false
                        var bracketCount = 0
                        
                        lines.forEachIndexed { index, line ->
                            val trimmedLine = line.trim()
                            
                            // 检测函数开始
                            if (trimmedLine.matches(Regex(".*fun\\s+\\w+.*\\{.*")) ||
                                (trimmedLine.matches(Regex(".*fun\\s+\\w+.*")) && lines.getOrNull(index + 1)?.contains("{") == true)) {
                                inFunction = true
                                currentFunctionLines = 1
                                currentFunctionTimberCount = 0
                                bracketCount = line.count { it == '{' } - line.count { it == '}' }
                            } else if (inFunction) {
                                currentFunctionLines++
                                bracketCount += line.count { it == '{' } - line.count { it == '}' }
                                
                                // 检测timber日志调用
                                if (trimmedLine.contains("Timber.") || trimmedLine.contains("timber.") || 
                                    trimmedLine.contains("log.")) {
                                    currentFunctionTimberCount++
                                }
                                
                                // 检测函数结束
                                if (bracketCount <= 0 && trimmedLine.contains("}")) {
                                    inFunction = false
                                    
                                    // 检查函数行数约束
                                    if (currentFunctionLines > 80) {
                                        logger.error("❌ 函数行数超限: ${file.relativeTo(projectDir)}:${index + 1} ($currentFunctionLines 行 > 80 行)")
                                        hasViolations = true
                                    }
                                    
                                    // 检查timber log约束
                                    if (currentFunctionTimberCount > 1) {
                                        logger.error("❌ Timber日志超限: ${file.relativeTo(projectDir)}:${index + 1} ($currentFunctionTimberCount 个 > 1 个)")
                                        hasViolations = true
                                    }
                                }
                            }
                        }
                    }
                    
                    if (hasViolations) {
                        println("❌ 发现代码约束违规，请修复后重试")
                        throw RuntimeException("GymBro项目约束验证失败")
                    } else {
                        println("✅ 所有文件符合GymBro项目约束")
                        println("✅ 函数行数 ≤ 80 行")
                        println("✅ 文件行数 ≤ 500 行") 
                        println("✅ Timber日志 ≤ 1 个/函数")
                        println("========================")
                    }
                }
            }

            // 让detektAll依赖于约束验证
            tasks.named("detektAll") {
                dependsOn("gymbroValidation")
            }

            // 创建格式化任务
            tasks.register("detektFormat") {
                description = "使用 Detekt 自动格式化 Kotlin 代码"
                group = "formatting"
                // 这个任务本身不执行任何操作，它仅作为触发器，
                // 通过 taskGraph.whenReady 中的逻辑来配置并运行实际的 Detekt 任务。
                // 添加 dependsOn 可以使任务关系更明确。
                dependsOn(tasks.withType<Detekt>())

                doFirst {
                    logger.warn("注意：Detekt 自动格式化会修改您的源代码。请确保在运行前已提交或备份您的更改。")
                }
            }


            // 创建baseline更新任务
            tasks.register("updateDetektBaseline") {
                description = "更新Detekt baseline文件"
                group = "verification"
                dependsOn(tasks.withType<DetektCreateBaselineTask>())

                doLast {
                    println("=== Detekt Baseline 已更新 ===")
                    println("Baseline文件：${rootProject.file("config/detekt/baseline.xml")}")
                    println("请提交更新的baseline文件到版本控制")
                    println("=============================")
                }
            }

            // 在Android项目中的特殊配置
            afterEvaluate {
                val isAndroidModule = project.plugins.hasPlugin("com.android.application") ||
                        project.plugins.hasPlugin("com.android.library")

                if (isAndroidModule) {
                    tasks.withType<Detekt>().configureEach {
                        // Android项目特定排除
                        exclude(
                            "**/R.kt",
                            "**/BuildConfig.kt",
                            "**/Manifest.kt",
                            "**/androidTest/**",
                            "**/debug/**",
                            "**/release/**"
                        )
                    }
                }
            }
        }
    }
}
