<PERSON><PERSON>MBRO TOTAL DEVELOPMENT PROTOCOL (GDP) V5.0 [ULTIMATE]
DOCUMENT ID: GDP-V5.0-ULTIMATE
EFFECTIVE DATE: IMMEDIATELY
STATUS: MANDATORY, NON-NEGOTIABLE
APPLICABILITY: ALL AI-DRIVEN CODE GENERATION, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AND REVIEW.
THIS DOCUMENT SUPERSEDES ALL PREVIOUS VERSIONS AND INSTRUCTIONS.


### SECTION 0: FUNDAMENTAL DIRECTIVES (THE UNBREAKABLE OATH)


0.1. THE PRIME DIRECTIVE: FACT-BASED CODING IS LAW.
    - YOUR PRIMARY ACTION IS NOT TO WRITE, BUT TO READ AND UNDERSTAND.
    - ALL GENERATED CODE MUST BE GROUNDED IN THE REALITY OF THE EXISTING CODEBASE.
    - YOU MUST USE REAL, EXISTING, AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> INTERFACES, <PERSON><PERSON><PERSON> MODELS, <PERSON><PERSON><PERSON><PERSON>, AND <PERSON><PERSON><PERSON>ITIES.
    - <PERSON>BR<PERSON>ATION OR ASSUMPTION OF ANY API, <PERSON>TH<PERSON> SIGNATURE, OR CLASS NAME IS A CRITICAL FAILURE AND IS STRICTLY FORBIDDEN.
    - BEFORE IMPLEMENTING A FEATURE, YOU MUST PERFORM A THOROUGH SEARCH OF THE PROJECT FOR SIMILAR PATTERNS.

0.2. THE TEMPLATE DIRECTIVE: READ EXAMPLES BEFORE WRITING.
    - THE '/code-examples' DIRECTORY IS NOT A SUGGESTION; IT IS YOUR TEMPLATE REPOSITORY.
    - YOUR GENERATED CODE MUST CONFORM TO THE STYLE, STRUCTURE, QUALITY, AND NUANCES DEMONSTRATED IN THESE EXAMPLES.
    - KEY TEMPLATES TO INTERNALIZE:
        * mvi_basic_implementation.kt: FOR ALL STANDARD MVI STRUCTURES.
        * function_call_integration.kt: FOR AI-RELATED FEATURE IMPLEMENTATION.
        * repository_implementation.kt: FOR ALL DATA LAYER LOGIC.
        * usecase_patterns.kt: FOR ALL DOMAIN LAYER LOGIC.
    - DEVIATION FROM THESE ESTABLISHED PATTERNS REQUIRES JUSTIFICATION AND IS GENERALLY DISCOURAGED.

0.3. THE SIMPLICITY DIRECTIVE: NO OVER-ENGINEERING.
    - THE PROJECT'S ARCHITECTURE IS INTENTIONALLY LEAN AND PURPOSEFUL.
    - YOU ARE PROHIBITED FROM INTRODUCING UNNECESSARY LAYERS OR ABSTRACTIONS.
    - FORBIDDEN PATTERNS INCLUDE, BUT ARE NOT LIMITED TO:
        * "Manager" or "Helper" classes with ambiguous responsibilities.
        * "Interactor" classes as an additional layer between ViewModel and UseCase.
        * Creating new base classes without explicit instruction.
        * Introducing complex generic systems where a simple, specific implementation will suffice.
    - ADHERE STRICTLY TO THE ESTABLISHED LAYERS: UI -> VIEWMODEL -> USECASE -> REPOSITORY.

0.4. THE COMPLETION DIRECTIVE: WRITE PURPOSEFUL AND COMPLETE CODE.
    - EVERY FILE YOU GENERATE OR MODIFY MUST BE FUNCTIONALLY COMPLETE AND COMPILABLE.
    - YOUR PRIMARY GOAL IS TO WRITE FULLY REALIZED FILES THAT ACHIEVE THE REQUESTED TASK.
    - THE FOLLOWING ARE STRICTLY FORBIDDEN:
        * EMPTY FUNCTION BODIES.
        * PLACEHOLDER COMMENTS (E.G., // TODO, // Implement later, // FIXME).
        * PRESET OR HARDCODED CONTENT THAT IS NOT PART OF THE CORE LOGIC.
        * PARTIALLY IMPLEMENTED LOGIC THAT RELIES ON NON-EXISTENT COMPONENTS.


### SECTION 1: GLOBAL ARCHITECTURAL PRINCIPLES (THE BLUEPRINT)


1.1. PRIMARY ARCHITECTURE: CLEAN ARCHITECTURE.
    - THIS IS THE BEDROCK OF THE ENTIRE APPLICATION.
    - IT ENSURES SEPARATION OF CONCERNS, TESTABILITY, AND MAINTAINABILITY.
    - EACH LAYER HAS A DISTINCT AND ENFORCED RESPONSIBILITY.

1.2. THE DEPENDENCY RED LINE: UNIDIRECTIONAL DEPENDENCY FLOW.
    - DEPENDENCIES MUST FLOW IN ONE DIRECTION ONLY: FEATURES -> DOMAIN -> DATA -> CORE.
    - THIS IS ENFORCED BY THE BUILD SYSTEM AND IS NON-NEGOTIABLE.
    - EXAMPLES OF VIOLATIONS:
        * FORBIDDEN: A 'domain' module class importing from a 'features' module.
        * FORBIDDEN: A 'features' module class directly importing from the 'data' module.
        * ALLOWED: A 'features' module class importing from the 'domain' module.

1.3. DEPENDENCY INJECTION (DI).
    - HILT IS THE SOLE AND MANDATORY DI FRAMEWORK.
    - ALL DEPENDENCIES MUST BE PROVIDED AND INJECTED VIA THEIR INTERFACES, NOT THEIR CONCRETE IMPLEMENTATIONS.
    - CONCRETE IMPLEMENTATIONS ARE BOUND TO INTERFACES ONLY WITHIN THE CONFINES OF A HILT DI MODULE.

1.4. ERROR HANDLING FRAMEWORK.
    - THE 'Result<T>' WRAPPER IS THE STANDARD MECHANISM FOR HANDLING OPERATIONS THAT CAN FAIL.
    - THIS PATTERN IS MANDATORY FOR ALL METHOD RETURN TYPES IN THE DATA AND DOMAIN LAYERS.
    - IT ELEGANTLY ENCAPSULATES EITHER A SUCCESS VALUE OR AN EXCEPTION.
    - VIEWMODELS AND REDUCERS MUST HANDLE THE 'Result' OBJECT TO UPDATE THE UI STATE ACCORDINGLY (E.G., SETTING 'state.error' or 'state.data').
    - RAW 'try-catch' BLOCKS FOR BUSINESS FLOW CONTROL WITHIN VIEWMODELS ARE FORBIDDEN.


### SECTION 2: MVI ARCHITECTURE CORE PROTOCOL (THE ENGINE)


2.1. UNIDIRECTIONAL DATA FLOW (UDF) AS THE SOLE UI PATTERN.
    - THE DATA FLOW IS A SACRED, IMMUTABLE LOOP:
        1. UI (USER INTERACTION) -> CREATES AND DISPATCHES INTENT.
        2. INTENT -> VIEWMODEL.
        3. VIEWMODEL -> PASSES CURRENT STATE AND INTENT TO REDUCER.
        4. REDUCER -> PURELY COMPUTES AND RETURNS A NEW STATE AND OPTIONAL EFFECTS.
        5. NEW STATE -> VIEWMODEL UPDATES ITS STATEFLOW.
        6. STATEFLOW -> UI OBSERVES AND RE-RENDERS.
        7. EFFECTS -> VIEWMODEL DISPATCHES TO EFFECT_HANDLER.
        8. EFFECT_HANDLER -> EXECUTES SIDE EFFECT (E.G., API CALL).
        9. RESULT OF SIDE EFFECT -> EFFECT_HANDLER CREATES A NEW '...Result' INTENT.
        10. NEW INTENT -> VIEWMODEL (CLOSING THE LOOP).

2.2. BASE CLASS REQUIREMENT.
    - ALL VIEWMODELS MUST INHERIT FROM THE PROJECT'S 'BaseMviViewModel'. THIS IS NOT OPTIONAL.

2.3. THE CONTRACT OBJECT.
    - EVERY FEATURE MODULE MUST DEFINE A PUBLIC 'Contract' OBJECT.
    - THIS OBJECT SERVES AS THE MODULE'S PUBLIC API, CLEARLY DOCUMENTING ITS CAPABILITIES.

2.4. STATE RULES.
    - STATE MUST BE AN @Immutable KOTLIN DATA CLASS. THIS IS CRITICAL FOR COMPOSE RECOMPOSITION PERFORMANCE.
    - ALL PROPERTIES OF THE STATE CLASS MUST BE DECLARED AS 'val'. THE STATE MUST BE DEEPLY IMMUTABLE.
    - THE STATE CLASS IS THE SINGLE, COMPREHENSIVE SOURCE OF TRUTH FOR THE ENTIRE SCREEN. IT SHOULD MODEL EVERYTHING THE UI NEEDS TO RENDER, INCLUDING LOADING INDICATORS, DATA LISTS, AND ERROR MESSAGES.
    - A VIEWMODEL MUST EXPOSE ONLY ONE StateFlow<State>. NO MULTIPLE STATE FLOWS.
    - DERIVED STATE (E.G., 'val isSubmitButtonEnabled: Boolean get() = name.isNotBlank() && email.isValid()') MUST BE A COMPUTED PROPERTY WITHIN THE STATE CLASS ITSELF. LOGIC TO DERIVE STATE IN THE UI LAYER IS FORBIDDEN.

2.5. INTENT RULES.
    - AN INTENT REPRESENTS A "DESIRE" TO CHANGE THE STATE.
    - NAMING MUST BE A VERB OR A GERUND, DESCRIBING THE ACTION (E.G., LoadUserProfile, UpdateSettings).
    - INTENTS THAT ORIGINATE FROM AN EFFECT_HANDLER (I.E., THE RESULT OF A SIDE EFFECT) MUST END WITH THE '...Result' SUFFIX. THIS NAMING CONVENTION IS A STRICT REQUIREMENT FOR DEBUGGING AND TRACEABILITY (E.G., LoadUserProfileResult, UpdateSettingsResult).

2.6. EFFECT RULES.
    - AN EFFECT REPRESENTS A "COMMAND" TO PERFORM A SIDE EFFECT.
    - NAMING MUST BE A VERB, DESCRIBING THE COMMAND (E.G., NavigateToHomeScreen, ShowErrorToast).
    - THE EFFECT IS A SIMPLE, DATA-CARRYING OBJECT. IT IS A RECIPE, NOT THE CHEF.
    - IT MUST NOT CONTAIN ANY EXECUTION LOGIC.
    - EACH EFFECT SHOULD REPRESENT A SINGLE, ATOMIC SIDE EFFECT.

2.7. COMPONENT RESPONSIBILITIES.
    - VIEWMODEL:
        - ROLE: ORCHESTRATOR / COORDINATOR.
        - RESPONSIBILITIES: HOLDING STATE, DISPATCHING INTENTS AND EFFECTS.
        - MUST DELEGATE ALL BUSINESS LOGIC TO DOMAIN LAYER USECASES.
        - MUST DELEGATE ALL SIDE EFFECT EXECUTION TO THE EFFECT_HANDLER.
    - REDUCER:
        - ROLE: PURE STATE TRANSFORMER.
        - MUST BE A PURE FUNCTION. GIVEN THE SAME INPUT STATE AND INTENT, IT MUST ALWAYS PRODUCE THE EXACT SAME OUTPUT STATE AND EFFECTS.
        - IT IS THE ONLY COMPONENT ALLOWED TO CREATE A NEW STATE INSTANCE.
        - IT IS STRICTLY FORBIDDEN FROM PERFORMING ANY SIDE EFFECTS (NO NETWORK, NO DATABASE, NO I/O, NO LOGGING, NO RANDOM NUMBER GENERATION).
    - EFFECT_HANDLER:
        - ROLE: SIDE EFFECT EXECUTOR.
        - IT IS THE ONLY COMPONENT ALLOWED TO INTERACT WITH THE "OUTSIDE WORLD" (NETWORK, DATABASE, SHARED PREFERENCES).
        - IT MUST CONVERT THE RESULT OF ANY ASYNCHRONOUS OPERATION INTO A NEW INTENT TO COMPLETE THE UDF LOOP.


### SECTION 3: CODE QUALITY & IMPLEMENTATION MANDATES (THE CRAFTSMANSHIP)


3.1. UI & COMPOSITION MANDATES.
    - COMPOSE-FIRST PHILOSOPHY: ALL NEW UI IS BUILT WITH JETPACK COMPOSE.
    - STATE HOISTING IS MANDATORY: COMPOSABLE FUNCTIONS SHOULD BE STATELESS WHENEVER POSSIBLE. THEY RECEIVE STATE AS PARAMETERS AND EXPOSE EVENTS VIA LAMBDAS. THE STATE IS "HOISTED" TO THE SCREEN-LEVEL COMPOSABLE THAT COLLECTS FROM THE VIEWMODEL.
    - FORBIDDEN IN COMPOSABLES: INITIATING NETWORK REQUESTS, QUERYING DATABASES, OR ANY OTHER FORM OF BUSINESS LOGIC. COMPOSABLES ARE FOR RENDERING UI BASED ON STATE, AND NOTHING ELSE.

3.2. DESIGN SYSTEM & CORE REUSABILITY MANDATES.
    - NO HARDCODED DESIGN VALUES: THIS IS A ZERO-TOLERANCE RULE. ALL DESIGN VALUES (COLORS, SPACING, RADII, ELEVATION, ANIMATION DURATIONS, FONT SIZES) MUST BE ACCESSED VIA THE 'designSystem' MODULE'S 'Tokens' SYSTEM. DIRECT USE OF '.dp' LITERALS OR HEX COLOR CODES IN FEATURE MODULES IS FORBIDDEN.
    - REUSE CORE UTILITIES: THE 'core' MODULE IS A SHARED, TRUSTED TOOLKIT. BEFORE WRITING ANY NEW UTILITY FUNCTION (E.G., FOR TIME FORMATTING, STRING MANIPULATION, DATA CONVERSIONS), YOU MUST FIRST EXHAUSTIVELY CHECK THE 'core' MODULE'S '/util' PACKAGES FOR AN EXISTING, APPROVED SOLUTION. DO NOT REINVENT THE WHEEL.
    - NO MAGIC NUMBERS: ALL PERFORMANCE-RELATED CONSTANTS (API TIMEOUTS, DEBOUNCE DURATIONS, ANIMATION STAGGERING DELAYS) MUST BE DEFINED IN AND ACCESSED FROM THE 'GlobalPerformanceConfig' OBJECT.

3.3. CODE CLEANLINESS AND STRUCTURE MANDATES.
    - NO PLACEHOLDER COMMENTS: FINAL, SUBMITTED CODE MUST BE FREE OF ALL 'TODO', 'FIXME', OR 'XXX' COMMENTS.
    - SINGLE RESPONSIBILITY PRINCIPLE (SRP): THIS APPLIES TO CLASSES AND FUNCTIONS. A FUNCTION SHOULD DO ONE THING WELL. A CLASS SHOULD HAVE ONE PRIMARY RESPONSIBILITY.
    - KEEP IT SMALL: AIM FOR SMALL, FOCUSED FUNCTIONS. AVOID FUNCTIONS THAT EXCEED 30-40 LINES. IF A FUNCTION IS TOO LONG, IT IS A SIGN IT IS DOING TOO MUCH AND MUST BE REFACTORED.


### SECTION 4: TESTING & VALIDATION PROTOCOL (THE DEFINITION OF DONE)


4.1. TESTING PHILOSOPHY.
    - TESTS ARE NOT AN AFTERTHOUGHT; THEY ARE PART OF THE IMPLEMENTATION.
    - DOMAIN LAYER: TEST PURE BUSINESS LOGIC.
    - DATA LAYER: TEST REPOSITORY LOGIC, DATA MAPPING, AND INTERACTIONS WITH DATA SOURCES.
    - VIEWMODEL/REDUCER: USE LIBRARIES LIKE TURBINE TO TEST STATE TRANSFORMATIONS AND EFFECT EMISSIONS IN RESPONSE TO A SEQUENCE OF INTENTS.

4.2. MINIMUM TEST COVERAGE THRESHOLDS (MANDATORY).
    - DOMAIN LAYER: >= 90%
    - DATA LAYER: >= 80%
    - VIEWMODEL & REDUCER: >= 75%

4.3. THE FINAL QUALITY CHECKPOINT (DEFINITION OF DONE).
    - A TASK OR FEATURE IS CONSIDERED 'DONE' IF AND ONLY IF ALL OF THE FOLLOWING COMMANDS PASS SUCCESSFULLY WITHOUT ANY WARNINGS OR ERRORS.
    - COMMAND SUITE:
        1. './gradlew formatCodeAll'
        2. './gradlew qualityCheckAll'
        3. './gradlew testAll'
        4. './gradlew coverageCheck'
        5. './gradlew assembleDebug'
    - FAILURE IN ANY OF THESE STEPS MEANS THE TASK IS NOT COMPLETE.

### END OF PROTOCOL ###
